'use client';
import { DFPManager } from 'react-dfp';
import { ClickTracker, GeoTracker, PageTracker } from '@benzinga/analytics';
import { AuthPortal } from '@benzinga/auth-ui';
import { NoFirstRender } from '@benzinga/hooks';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { PagePropsType } from '../../../utils/getServerProps';
import { Meta, MetaProps, Schema } from '@benzinga/seo';
import { SEGMENT_KEY } from '../../../src/env';
import { formatNavigationSchema } from '@benzinga/frontend-utils';
import { MainMenu } from '@benzinga/navigation-ui';
import { useSophiTracking } from '@benzinga/user-context';
import { sophiManager } from '@benzinga/ads-utils';

interface RootTrackerProps {
  pageProps?: PagePropsType;
}

const RootTracker: React.FC<RootTrackerProps> = ({ pageProps }) => {
  const [meta, setMeta] = useState<MetaProps>(pageProps?.metaProps as MetaProps);

  const router = useRouter();
  const pathname = usePathname();
  const canonical = meta?.canonical ?? `https://www.benzinga.com${pathname}`;
  const shouldPageTrack = pageProps?.statusCode !== 404 && !pageProps?.disablePageTracking && pageProps?.error !== 404;

  const handleOnRegister = React.useCallback(() => {
    router.push('/welcome');
  }, [router]);

  useEffect(() => {
    const listener = (e: Event) => {
      const customEvent = e as CustomEvent<any>;
      if (customEvent?.detail?.trackMeta !== false) {
        setMeta(customEvent?.detail);
      }
    };
    window.addEventListener('page-transition', listener);
    return () => {
      window.removeEventListener('page-transition', listener);
    };
  }, []);

  const { trackPageView } = useSophiTracking({
    article: pageProps?.article,
    meta: pageProps?.metaProps,
    post: pageProps?.post || pageProps?.page,
    term: pageProps?.term,
    pageTargeting: pageProps?.pageTargeting,
  });

  useEffect(() => {
    (async () => {
      sophiManager.init(() => {
        trackPageView();
      });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <NoFirstRender>
        <React.Suspense fallback={null}>
          <ClickTracker article={pageProps?.article} post={pageProps?.post || pageProps?.page} />
          <GeoTracker />
          <AuthPortal authMode="register" onRegister={handleOnRegister} />
        </React.Suspense>
      </NoFirstRender>
      {shouldPageTrack && <PageTracker enableCoralogixRum={true} routerType="app" />}
      {meta && <Meta {...meta} canonical={canonical} />}
      <Schema data={formatNavigationSchema(MainMenu.primary)} name="navigation-header" />
    </>
  );
};

export default RootTracker;
