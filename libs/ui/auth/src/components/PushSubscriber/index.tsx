import React, { useEffect, useState } from 'react';
import { FaBell } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import { SessionContext } from '@benzinga/session-context';
import { NotificationManager } from '@benzinga/notification-manager';
import { TrackingManager } from '@benzinga/tracking-manager';

export const PushSubscriber = () => {
  const { t } = useTranslation(['common']);
  const session = React.useContext(SessionContext);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const prompt = JSON.parse(localStorage.getItem('notification-prompt') ?? '{}');
    const nextPrompt = prompt.timestamp + 1000 * 60 * 60 * 24 * 7;
    const shouldShow = !prompt.timestamp || Date.now() > nextPrompt;

    if (!shouldShow) return;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const scrollTrigger = windowHeight * 0.1;

      if (scrollTop >= scrollTrigger) {
        setShow(true);
        window.removeEventListener('scroll', handleScroll);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  if (!('Notification' in window) || !show || Notification?.permission === 'granted') return null;

  const handleAllow = async () => {
    setShow(false);

    if (Notification.permission !== 'granted') {
      Notification.requestPermission().then(async permission => {
        if (permission !== 'granted') {
          alert(t('Notifications.disabled-settings-alert'));
          return;
        }
      });
    }

    const result = await session
      ?.getManager(NotificationManager)
      .setupServiceWorker({ global_key: window.location.host });
    if (!result.ok?.success) {
      alert(t('Notifications.unable-to-subscribe-alert'));
    } else {
      session
        .getManager(TrackingManager)
        .trackNotificationEvent('subscribe', { notification_id: window.location.host, notification_type: 'push' });
    }
  };

  const handleDeny = () => {
    localStorage.setItem('notification-prompt', JSON.stringify({ timestamp: Date.now(), value: 'dismissed' }));
    setShow(false);
  };

  return (
    <div className="fixed top-[112px] w-full z-[1000] animate-fade-down">
      <div className="border bg-white p-8 pb-4 max-w-md rounded-b-sm mx-auto shadow-xl">
        <div className="flex flex-row gap-2 pb-4">
          <FaBell className="w-32" color="lightgray" size={42} />
          <div>
            <p className="text-base leading-tight">{t('Notifications.get-push-notifications-prompt')}</p>
            <p className="text-xs mt-2 leading-tight text-gray-600">
              {t('Notifications.disable-notifications-through-browser')}
            </p>
          </div>
        </div>
        <div className="flex flex-row items-center justify-end gap-4">
          <button className="text-bzblue-700 px-4 py-2" onClick={handleDeny}>
            {t('Buttons.deny')}
          </button>
          <button className="bg-bzblue-700 text-white px-4 py-2 rounded-md" onClick={handleAllow}>
            {t('Buttons.allow')}
          </button>
        </div>
      </div>
    </div>
  );
};
