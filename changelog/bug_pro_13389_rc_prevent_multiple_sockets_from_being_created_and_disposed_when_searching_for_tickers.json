{"affectedProjects": ["bz", "bz-mobile", "data-managers-examples", "india", "money", "newsdesk-tools", "pro", "proto", "test-quote", "widgets-pro-calendar", "widgets-pro-insiders"], "description": "rc prevent multiple sockets from being created and disposed when searching for tickers", "epic": null, "issueNumber": "13389", "project": "PRO", "projects": ["bz", "bz-mobile", "data-managers-examples", "india", "money", "newsdesk-tools", "pro", "proto", "test-quote", "widgets-pro-calendar", "widgets-pro-insiders"], "type": "bug", "updatedAt": "2025-06-25T13:39:53.954Z"}